# GlossaFlow Development Environment Setup Guide

This guide will walk you through setting up the complete development environment for GlossaFlow.

## Prerequisites

- Node.js 18+ installed
- Git installed
- A Supabase account
- A Stripe account (for payment processing)
- Google/GitHub OAuth apps (for authentication)

## 1. Supabase Setup

### Step 1: Create a New Supabase Project

1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Click "New Project"
3. Choose your organization
4. Fill in project details:
   - **Name**: `glossaflow-dev` (or your preferred name)
   - **Database Password**: Generate a strong password and save it
   - **Region**: Choose the closest region to your users
5. Click "Create new project"

### Step 2: Get Supabase Credentials

Once your project is created, go to **Settings > API**:

- **Project URL**: `https://your-project-id.supabase.co`
- **Anon Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` (public key)
- **Service Role Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` (secret key)

### Step 3: Configure Authentication Providers

Go to **Authentication > Providers**:

1. **Enable Email Provider**:
   - Toggle "Enable email confirmations" if desired
   - Set "Site URL" to `http://localhost:3000`

2. **Enable Google OAuth**:
   - Toggle Google provider
   - Add your Google OAuth credentials (see OAuth setup below)

3. **Enable GitHub OAuth**:
   - Toggle GitHub provider  
   - Add your GitHub OAuth credentials (see OAuth setup below)

### Step 4: Set URL Configuration

Go to **Authentication > URL Configuration**:
- **Site URL**: `http://localhost:3000`
- **Redirect URLs**: Add `http://localhost:3000/api/auth/callback/google` and `http://localhost:3000/api/auth/callback/github`

## 2. OAuth Providers Setup

### Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Go to **Credentials > Create Credentials > OAuth 2.0 Client IDs**
5. Configure OAuth consent screen if needed
6. Create OAuth 2.0 Client ID:
   - **Application type**: Web application
   - **Name**: GlossaFlow Development
   - **Authorized redirect URIs**: 
     - `http://localhost:3000/api/auth/callback/google`
     - `https://your-project-id.supabase.co/auth/v1/callback`
7. Save the **Client ID** and **Client Secret**

### GitHub OAuth Setup

1. Go to [GitHub Developer Settings](https://github.com/settings/developers)
2. Click "New OAuth App"
3. Fill in the details:
   - **Application name**: GlossaFlow Development
   - **Homepage URL**: `http://localhost:3000`
   - **Authorization callback URL**: `http://localhost:3000/api/auth/callback/github`
4. Click "Register application"
5. Save the **Client ID** and **Client Secret**

## 3. Stripe Setup

### Step 1: Create Stripe Account

1. Go to [Stripe Dashboard](https://dashboard.stripe.com/)
2. Create an account or sign in
3. Activate your account (you can use test mode for development)

### Step 2: Get API Keys

Go to **Developers > API Keys**:
- **Publishable key**: `pk_test_...` (for frontend)
- **Secret key**: `sk_test_...` (for backend)

### Step 3: Set up Webhooks

1. Go to **Developers > Webhooks**
2. Click "Add endpoint"
3. **Endpoint URL**: `http://localhost:3000/api/webhooks/stripe`
4. **Events to send**:
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
5. Save the **Webhook Secret** (`whsec_...`)

## 4. Environment Variables Setup

Create a `.env.local` file in your project root:

```env
# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-here

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# OAuth Providers
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Redis Configuration (Optional - for production)
UPSTASH_REDIS_REST_URL=your-redis-url
UPSTASH_REDIS_REST_TOKEN=your-redis-token

# Email Configuration (Optional)
EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>
```

## 5. Generate NextAuth Secret

Run this command to generate a secure NextAuth secret:

```bash
openssl rand -base64 32
```

Copy the output and use it as your `NEXTAUTH_SECRET`.

## 6. Verify Setup

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Run the development server**:
   ```bash
   npm run dev
   ```

3. **Test the application**:
   - Open `http://localhost:3000`
   - Verify the dashboard loads correctly
   - Check browser console for any errors

## 7. Optional: Redis Setup (for Production)

For production caching and rate limiting:

1. Create an account at [Upstash](https://upstash.com/)
2. Create a new Redis database
3. Get the REST URL and token
4. Add them to your environment variables

## Troubleshooting

### Common Issues:

1. **Supabase Connection Error**:
   - Verify your project URL and API keys
   - Check if your IP is allowed (Supabase allows all by default)

2. **OAuth Redirect Mismatch**:
   - Ensure redirect URLs match exactly in OAuth provider settings
   - Check for trailing slashes

3. **Stripe Webhook Issues**:
   - Use ngrok for local webhook testing: `ngrok http 3000`
   - Update webhook URL to ngrok URL for local development

4. **Environment Variables Not Loading**:
   - Restart your development server after changing `.env.local`
   - Ensure no spaces around the `=` sign in environment variables

## Next Steps

Once your environment is configured:
1. Run the database migrations (next task)
2. Test authentication flows
3. Set up your first organization and project

For detailed API documentation and database schema, see the respective documentation files.
