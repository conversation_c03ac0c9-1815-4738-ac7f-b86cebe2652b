import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { organizationsApi } from './api/organizationsApi';
import { projectsApi } from './api/projectsApi';
import { translationApi } from './api/translationApi';
import { terminologyApi } from './api/terminologyApi';
import authSlice from './slices/authSlice';
import uiSlice from './slices/uiSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    ui: uiSlice,
    [organizationsApi.reducerPath]: organizationsApi.reducer,
    [projectsApi.reducerPath]: projectsApi.reducer,
    [translationApi.reducerPath]: translationApi.reducer,
    [terminologyApi.reducerPath]: terminologyApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    })
      .concat(organizationsApi.middleware)
      .concat(projectsApi.middleware)
      .concat(translationApi.middleware)
      .concat(terminologyApi.middleware),
});

// Setup listeners for automatic refetching
setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
