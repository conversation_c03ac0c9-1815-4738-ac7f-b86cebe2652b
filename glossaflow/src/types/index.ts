// Application types for GlossaFlow
import { Database } from './database';

// Database table types
export type Organization = Database['public']['Tables']['organizations']['Row'];
export type User = Database['public']['Tables']['users']['Row'];
export type OrganizationMember = Database['public']['Tables']['organization_members']['Row'];
export type Project = Database['public']['Tables']['projects']['Row'];
export type TranslationSegment = Database['public']['Tables']['translation_segments']['Row'];
export type TerminologyEntry = Database['public']['Tables']['terminology_entries']['Row'];

// Insert types
export type CreateOrganizationRequest = Database['public']['Tables']['organizations']['Insert'];
export type CreateProjectRequest = Database['public']['Tables']['projects']['Insert'];
export type CreateSegmentRequest = Database['public']['Tables']['translation_segments']['Insert'];
export type CreateTerminologyRequest = Database['public']['Tables']['terminology_entries']['Insert'];

// Update types
export type UpdateOrganizationRequest = Database['public']['Tables']['organizations']['Update'];
export type UpdateProjectRequest = Database['public']['Tables']['projects']['Update'];
export type UpdateSegmentRequest = Database['public']['Tables']['translation_segments']['Update'];
export type UpdateTerminologyRequest = Database['public']['Tables']['terminology_entries']['Update'];

// Re-export enum types
export * from './database';

// API Response types
export interface ApiResponse<T> {
  data: T;
  error?: string;
  message?: string;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface GetOrganizationsResponse {
  organizations: Array<Organization & {
    role: UserRole;
    memberCount: number;
    projectCount: number;
  }>;
}

export interface GetOrganizationResponse {
  organization: Organization;
  members: OrganizationMember[];
  projects: ProjectSummary[];
  subscription: SubscriptionInfo;
}

export interface GetProjectsResponse {
  projects: ProjectSummary[];
  pagination: PaginationInfo;
}

export interface GetProjectResponse {
  project: Project;
  documents: SourceDocument[];
  team: ProjectTeamMember[];
  progress: ProjectProgress;
  terminology: TerminologyEntry[];
}

export interface GetSegmentsResponse {
  segments: TranslationSegment[];
  pagination: PaginationInfo;
  filters: {
    statuses: SegmentStatus[];
    translators: User[];
    reviewers: User[];
  };
}

export interface GetSegmentResponse {
  segment: TranslationSegment;
  terminology: TerminologyMatch[];
  translationMemory: TMMatch[];
  comments: Comment[];
  reviews: Review[];
  context: {
    previousSegments: TranslationSegment[];
    nextSegments: TranslationSegment[];
  };
}

export interface GetTerminologyResponse {
  entries: TerminologyEntry[];
  pagination: PaginationInfo;
  categories: string[];
  statistics: {
    totalEntries: number;
    approvedEntries: number;
    pendingEntries: number;
  };
}

// Additional types
export interface ProjectSummary {
  id: string;
  name: string;
  description: string | null;
  status: ProjectStatus;
  priority: Priority;
  deadline: string | null;
  progress: {
    totalSegments: number;
    completedSegments: number;
    reviewedSegments: number;
    approvedSegments: number;
  };
  team: {
    translators: number;
    reviewers: number;
  };
  created_at: string;
  updated_at: string;
}

export interface SourceDocument {
  id: string;
  project_id: string;
  filename: string;
  file_path: string;
  file_size: number | null;
  mime_type: string | null;
  upload_status: string;
  segment_count: number;
  uploaded_by: string | null;
  created_at: string;
}

export interface ProjectTeamMember {
  id: string;
  user_id: string;
  role: UserRole;
  user: User;
  assignedSegments?: number;
  completedSegments?: number;
}

export interface ProjectProgress {
  totalSegments: number;
  pendingSegments: number;
  inProgressSegments: number;
  translatedSegments: number;
  reviewedSegments: number;
  approvedSegments: number;
  rejectedSegments: number;
  completionPercentage: number;
}

export interface TerminologyMatch {
  entry: TerminologyEntry;
  confidence: number;
  context: string;
}

export interface TMMatch {
  sourceText: string;
  targetText: string;
  similarity: number;
  origin: string;
  lastUsed: string;
}

export interface Comment {
  id: string;
  segment_id: string;
  user_id: string;
  content: string;
  comment_type: CommentType;
  parent_comment_id: string | null;
  resolved: boolean;
  user: User;
  replies?: Comment[];
  created_at: string;
  updated_at: string;
}

export interface Review {
  id: string;
  segment_id: string;
  reviewer_id: string;
  review_type: ReviewType;
  status: ReviewStatus;
  feedback: string | null;
  quality_score: number | null;
  reviewer: User;
  reviewed_at: string;
  created_at: string;
}

export interface SubscriptionInfo {
  id: string;
  organization_id: string;
  stripe_subscription_id: string | null;
  status: string;
  tier: string;
  current_period_start: string | null;
  current_period_end: string | null;
  cancel_at_period_end: boolean;
  created_at: string;
  updated_at: string;
}

// Form types
export interface OrganizationOnboarding {
  step1?: {
    organizationName: string;
    industry: string;
    teamSize: string;
  };
  step2?: {
    primaryLanguages: LanguagePair[];
    projectTypes: DocumentType[];
    workflowPreferences: WorkflowConfig;
  };
  step3?: {
    subscriptionTier: string;
    billingInfo: any;
  };
}

export interface LanguagePair {
  source: string;
  target: string;
}

export interface WorkflowConfig {
  reviewStages: ReviewStage[];
  autoAssignment: boolean;
  qualityThreshold: number;
}

export interface ReviewStage {
  name: string;
  reviewerRole: UserRole;
  requiredApprovals: number;
  autoAdvance: boolean;
}

export interface StyleGuideConfig {
  characterVoices: CharacterVoice[];
  translationGuidelines: TranslationGuidelines;
  qualityStandards: QualityStandards;
}

export interface CharacterVoice {
  characterName: string;
  pronouns: string;
  speechPattern: string;
  formalityLevel: string;
  consistencyRules: string[];
}

export interface TranslationGuidelines {
  culturalAdaptation: string;
  honorifics: any;
  nameTranslation: string;
  numbersAndDates: any;
}

export interface QualityStandards {
  maxSegmentLength: number;
  requiredTerminologyUsage: boolean;
  consistencyThreshold: number;
  reviewRequirements: any;
}

// Collaboration types
export interface CollaborationUser {
  userId: string;
  userName: string;
  avatarUrl: string | null;
  timestamp: string;
}

export interface CursorPosition {
  userId: string;
  position: number;
  selection: [number, number];
  timestamp: string;
}

// Quality assurance types
export interface QualityIssue {
  type: string;
  severity: 'error' | 'warning' | 'info';
  message: string;
  suggestion?: string;
  position?: number;
}

// Utility types
export interface FieldMapping {
  sourceTerm: string;
  targetTerm: string;
  targetLanguage: string;
  category?: string;
  context?: string;
  usageNotes?: string;
}
