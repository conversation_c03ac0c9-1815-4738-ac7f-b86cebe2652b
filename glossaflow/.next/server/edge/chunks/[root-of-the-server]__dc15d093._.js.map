{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { withAuth } from 'next-auth/middleware';\nimport { NextResponse } from 'next/server';\n\nexport default withAuth(\n  function middleware(req) {\n    const { pathname } = req.nextUrl;\n    const token = req.nextauth.token;\n\n    // Public routes that don't require authentication\n    const publicRoutes = ['/auth/signin', '/auth/signup', '/auth/error', '/auth/verify-request'];\n    \n    // If user is authenticated and trying to access auth pages, redirect to dashboard\n    if (token && publicRoutes.includes(pathname)) {\n      return NextResponse.redirect(new URL('/dashboard', req.url));\n    }\n\n    // If user is not authenticated and trying to access protected routes\n    if (!token && !publicRoutes.includes(pathname) && pathname !== '/') {\n      return NextResponse.redirect(new URL('/auth/signin', req.url));\n    }\n\n    return NextResponse.next();\n  },\n  {\n    callbacks: {\n      authorized: ({ token, req }) => {\n        const { pathname } = req.nextUrl;\n        \n        // Allow access to public routes\n        const publicRoutes = ['/auth/signin', '/auth/signup', '/auth/error', '/auth/verify-request'];\n        if (publicRoutes.includes(pathname)) {\n          return true;\n        }\n\n        // Require authentication for all other routes\n        return !!token;\n      },\n    },\n  }\n);\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;uCAEe,CAAA,GAAA,kJAAA,CAAA,WAAQ,AAAD,EACpB,SAAS,WAAW,GAAG;IACrB,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,OAAO;IAChC,MAAM,QAAQ,IAAI,QAAQ,CAAC,KAAK;IAEhC,kDAAkD;IAClD,MAAM,eAAe;QAAC;QAAgB;QAAgB;QAAe;KAAuB;IAE5F,kFAAkF;IAClF,IAAI,SAAS,aAAa,QAAQ,CAAC,WAAW;QAC5C,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,IAAI,GAAG;IAC5D;IAEA,qEAAqE;IACrE,IAAI,CAAC,SAAS,CAAC,aAAa,QAAQ,CAAC,aAAa,aAAa,KAAK;QAClE,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,gBAAgB,IAAI,GAAG;IAC9D;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B,GACA;IACE,WAAW;QACT,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE;YACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,OAAO;YAEhC,gCAAgC;YAChC,MAAM,eAAe;gBAAC;gBAAgB;gBAAgB;gBAAe;aAAuB;YAC5F,IAAI,aAAa,QAAQ,CAAC,WAAW;gBACnC,OAAO;YACT;YAEA,8CAA8C;YAC9C,OAAO,CAAC,CAAC;QACX;IACF;AACF;AAGK,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}