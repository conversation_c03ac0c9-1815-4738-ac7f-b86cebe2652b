{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "tAmvl4mg1oDWe5Dsprh8Ih7p7p0YuTcmWBNmQzoBO34=", "__NEXT_PREVIEW_MODE_ID": "a40d6cfd68a79ff1ab4671238241875d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "96aa95e7e3646089cfc86a3078a5b9358030b39c0add05171f5da0a2d81cc522", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5c6a17fd8490a768b6d5b2d6bfd7a693329e3730bf28ff7e21f7ace319d99db8"}}}, "sortedMiddleware": ["/"], "functions": {}}