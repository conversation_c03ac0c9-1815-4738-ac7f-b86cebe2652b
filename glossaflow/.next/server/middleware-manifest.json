{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_ed853b78._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_dbd282d0.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "tAmvl4mg1oDWe5Dsprh8Ih7p7p0YuTcmWBNmQzoBO34=", "__NEXT_PREVIEW_MODE_ID": "72ec9b66c6d031f0088814f9e8a2f1d4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "131ce6860bf6629a8ede5cab460b77a8ce1181a4b78b1525df1cea46e1b23341", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "24f63bb3b2de93587a2ef6f8eed84686b9788d18a5292412b1307d493c910cbc"}}}, "sortedMiddleware": ["/"], "functions": {}}