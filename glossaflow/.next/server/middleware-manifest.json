{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "tAmvl4mg1oDWe5Dsprh8Ih7p7p0YuTcmWBNmQzoBO34=", "__NEXT_PREVIEW_MODE_ID": "2d3a57e3ad08d144f7842443bbfeb3fd", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "793c11a1024af0da2c57df358295a98f696ae516b35cd2ade626dcf4c434dd56", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "13c8f74c62f1e2479203799fd79c53283ab5f1c29ff9afdc2979bdefe4caa2cd"}}}, "sortedMiddleware": ["/"], "functions": {}}