{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/store/api/organizationsApi.ts"], "sourcesContent": ["import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';\nimport {\n  Organization,\n  CreateOrganizationRequest,\n  UpdateOrganizationRequest,\n  GetOrganizationsResponse,\n  GetOrganizationResponse,\n  InviteUserRequest,\n} from '@/types';\nimport { RootState } from '../index';\n\nexport const organizationsApi = createApi({\n  reducerPath: 'organizationsApi',\n  baseQuery: fetchBaseQuery({\n    baseUrl: '/api/organizations',\n    prepareHeaders: (headers, { getState }) => {\n      // Add authentication headers if needed\n      headers.set('Content-Type', 'application/json');\n      return headers;\n    },\n  }),\n  tagTypes: ['Organization', 'Member', 'Subscription'],\n  endpoints: (builder) => ({\n    getOrganizations: builder.query<GetOrganizationsResponse, void>({\n      query: () => '',\n      providesTags: ['Organization'],\n    }),\n    createOrganization: builder.mutation<Organization, CreateOrganizationRequest>({\n      query: (body) => ({\n        url: '',\n        method: 'POST',\n        body,\n      }),\n      invalidatesTags: ['Organization'],\n    }),\n    getOrganization: builder.query<GetOrganizationResponse, string>({\n      query: (orgId) => `/${orgId}`,\n      providesTags: (result, error, orgId) => [\n        { type: 'Organization', id: orgId },\n        'Member',\n        'Subscription',\n      ],\n    }),\n    updateOrganization: builder.mutation<\n      Organization,\n      { orgId: string; data: UpdateOrganizationRequest }\n    >({\n      query: ({ orgId, data }) => ({\n        url: `/${orgId}`,\n        method: 'PUT',\n        body: data,\n      }),\n      invalidatesTags: (result, error, { orgId }) => [\n        { type: 'Organization', id: orgId },\n      ],\n    }),\n    inviteUser: builder.mutation<\n      void,\n      { orgId: string; data: InviteUserRequest }\n    >({\n      query: ({ orgId, data }) => ({\n        url: `/${orgId}/invite`,\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: ['Member'],\n    }),\n    removeMember: builder.mutation<\n      void,\n      { orgId: string; memberId: string }\n    >({\n      query: ({ orgId, memberId }) => ({\n        url: `/${orgId}/members/${memberId}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: ['Member'],\n    }),\n    updateMemberRole: builder.mutation<\n      void,\n      { orgId: string; memberId: string; role: string }\n    >({\n      query: ({ orgId, memberId, role }) => ({\n        url: `/${orgId}/members/${memberId}`,\n        method: 'PUT',\n        body: { role },\n      }),\n      invalidatesTags: ['Member'],\n    }),\n  }),\n});\n\nexport const {\n  useGetOrganizationsQuery,\n  useCreateOrganizationMutation,\n  useGetOrganizationQuery,\n  useUpdateOrganizationMutation,\n  useInviteUserMutation,\n  useRemoveMemberMutation,\n  useUpdateMemberRoleMutation,\n} = organizationsApi;\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAAA;;AAWO,MAAM,mBAAmB,CAAA,GAAA,kNAAA,CAAA,YAAS,AAAD,EAAE;IACxC,aAAa;IACb,WAAW,CAAA,GAAA,gLAAA,CAAA,iBAAc,AAAD,EAAE;QACxB,SAAS;QACT,gBAAgB,CAAC,SAAS,EAAE,QAAQ,EAAE;YACpC,uCAAuC;YACvC,QAAQ,GAAG,CAAC,gBAAgB;YAC5B,OAAO;QACT;IACF;IACA,UAAU;QAAC;QAAgB;QAAU;KAAe;IACpD,WAAW,CAAC,UAAY,CAAC;YACvB,kBAAkB,QAAQ,KAAK,CAAiC;gBAC9D,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAe;YAChC;YACA,oBAAoB,QAAQ,QAAQ,CAA0C;gBAC5E,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR;oBACF,CAAC;gBACD,iBAAiB;oBAAC;iBAAe;YACnC;YACA,iBAAiB,QAAQ,KAAK,CAAkC;gBAC9D,OAAO,CAAC,QAAU,CAAC,CAAC,EAAE,OAAO;gBAC7B,cAAc,CAAC,QAAQ,OAAO,QAAU;wBACtC;4BAAE,MAAM;4BAAgB,IAAI;wBAAM;wBAClC;wBACA;qBACD;YACH;YACA,oBAAoB,QAAQ,QAAQ,CAGlC;gBACA,OAAO,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,GAAK,CAAC;wBAC3B,KAAK,CAAC,CAAC,EAAE,OAAO;wBAChB,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,KAAK,EAAE,GAAK;wBAC7C;4BAAE,MAAM;4BAAgB,IAAI;wBAAM;qBACnC;YACH;YACA,YAAY,QAAQ,QAAQ,CAG1B;gBACA,OAAO,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,GAAK,CAAC;wBAC3B,KAAK,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC;wBACvB,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAS;YAC7B;YACA,cAAc,QAAQ,QAAQ,CAG5B;gBACA,OAAO,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAK,CAAC;wBAC/B,KAAK,CAAC,CAAC,EAAE,MAAM,SAAS,EAAE,UAAU;wBACpC,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;iBAAS;YAC7B;YACA,kBAAkB,QAAQ,QAAQ,CAGhC;gBACA,OAAO,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAK,CAAC;wBACrC,KAAK,CAAC,CAAC,EAAE,MAAM,SAAS,EAAE,UAAU;wBACpC,QAAQ;wBACR,MAAM;4BAAE;wBAAK;oBACf,CAAC;gBACD,iBAAiB;oBAAC;iBAAS;YAC7B;QACF,CAAC;AACH;AAEO,MAAM,EACX,wBAAwB,EACxB,6BAA6B,EAC7B,uBAAuB,EACvB,6BAA6B,EAC7B,qBAAqB,EACrB,uBAAuB,EACvB,2BAA2B,EAC5B,GAAG", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/store/api/projectsApi.ts"], "sourcesContent": ["import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';\nimport {\n  Project,\n  CreateProjectRequest,\n  UpdateProjectRequest,\n  GetProjectsResponse,\n  GetProjectResponse,\n  GetSegmentsResponse,\n  SourceDocument,\n} from '@/types';\n\ninterface GetProjectsParams {\n  orgId?: string;\n  status?: string;\n  page?: number;\n  limit?: number;\n}\n\ninterface GetSegmentsParams {\n  projectId: string;\n  page?: number;\n  limit?: number;\n  status?: string;\n  translatorId?: string;\n  reviewerId?: string;\n}\n\nexport const projectsApi = createApi({\n  reducerPath: 'projectsApi',\n  baseQuery: fetchBaseQuery({\n    baseUrl: '/api/projects',\n    prepareHeaders: (headers) => {\n      headers.set('Content-Type', 'application/json');\n      return headers;\n    },\n  }),\n  tagTypes: ['Project', 'Segment', 'Document', 'Terminology'],\n  endpoints: (builder) => ({\n    getProjects: builder.query<GetProjectsResponse, GetProjectsParams>({\n      query: (params) => ({\n        url: '',\n        params,\n      }),\n      providesTags: ['Project'],\n    }),\n    createProject: builder.mutation<Project, CreateProjectRequest>({\n      query: (body) => ({\n        url: '',\n        method: 'POST',\n        body,\n      }),\n      invalidatesTags: ['Project'],\n    }),\n    getProject: builder.query<GetProjectResponse, string>({\n      query: (projectId) => `/${projectId}`,\n      providesTags: (result, error, projectId) => [\n        { type: 'Project', id: projectId },\n        'Document',\n        'Terminology',\n      ],\n    }),\n    updateProject: builder.mutation<\n      Project,\n      { projectId: string; data: UpdateProjectRequest }\n    >({\n      query: ({ projectId, data }) => ({\n        url: `/${projectId}`,\n        method: 'PUT',\n        body: data,\n      }),\n      invalidatesTags: (result, error, { projectId }) => [\n        { type: 'Project', id: projectId },\n      ],\n    }),\n    deleteProject: builder.mutation<void, string>({\n      query: (projectId) => ({\n        url: `/${projectId}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: ['Project'],\n    }),\n    uploadDocuments: builder.mutation<\n      SourceDocument[],\n      { projectId: string; data: FormData }\n    >({\n      query: ({ projectId, data }) => ({\n        url: `/${projectId}/documents`,\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: ['Document', 'Segment'],\n    }),\n    getSegments: builder.query<GetSegmentsResponse, GetSegmentsParams>({\n      query: ({ projectId, ...params }) => ({\n        url: `/${projectId}/segments`,\n        params,\n      }),\n      providesTags: ['Segment'],\n    }),\n    assignTranslator: builder.mutation<\n      void,\n      { projectId: string; segmentIds: string[]; translatorId: string }\n    >({\n      query: ({ projectId, segmentIds, translatorId }) => ({\n        url: `/${projectId}/assign-translator`,\n        method: 'POST',\n        body: { segmentIds, translatorId },\n      }),\n      invalidatesTags: ['Segment'],\n    }),\n    assignReviewer: builder.mutation<\n      void,\n      { projectId: string; segmentIds: string[]; reviewerId: string }\n    >({\n      query: ({ projectId, segmentIds, reviewerId }) => ({\n        url: `/${projectId}/assign-reviewer`,\n        method: 'POST',\n        body: { segmentIds, reviewerId },\n      }),\n      invalidatesTags: ['Segment'],\n    }),\n    getProjectStats: builder.query<\n      {\n        totalSegments: number;\n        completedSegments: number;\n        pendingSegments: number;\n        reviewedSegments: number;\n        approvedSegments: number;\n      },\n      string\n    >({\n      query: (projectId) => `/${projectId}/stats`,\n      providesTags: (result, error, projectId) => [\n        { type: 'Project', id: `${projectId}-stats` },\n      ],\n    }),\n  }),\n});\n\nexport const {\n  useGetProjectsQuery,\n  useCreateProjectMutation,\n  useGetProjectQuery,\n  useUpdateProjectMutation,\n  useDeleteProjectMutation,\n  useUploadDocumentsMutation,\n  useGetSegmentsQuery,\n  useAssignTranslatorMutation,\n  useAssignReviewerMutation,\n  useGetProjectStatsQuery,\n} = projectsApi;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AAAA;;AA2BO,MAAM,cAAc,CAAA,GAAA,kNAAA,CAAA,YAAS,AAAD,EAAE;IACnC,aAAa;IACb,WAAW,CAAA,GAAA,gLAAA,CAAA,iBAAc,AAAD,EAAE;QACxB,SAAS;QACT,gBAAgB,CAAC;YACf,QAAQ,GAAG,CAAC,gBAAgB;YAC5B,OAAO;QACT;IACF;IACA,UAAU;QAAC;QAAW;QAAW;QAAY;KAAc;IAC3D,WAAW,CAAC,UAAY,CAAC;YACvB,aAAa,QAAQ,KAAK,CAAyC;gBACjE,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK;wBACL;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAU;YAC3B;YACA,eAAe,QAAQ,QAAQ,CAAgC;gBAC7D,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR;oBACF,CAAC;gBACD,iBAAiB;oBAAC;iBAAU;YAC9B;YACA,YAAY,QAAQ,KAAK,CAA6B;gBACpD,OAAO,CAAC,YAAc,CAAC,CAAC,EAAE,WAAW;gBACrC,cAAc,CAAC,QAAQ,OAAO,YAAc;wBAC1C;4BAAE,MAAM;4BAAW,IAAI;wBAAU;wBACjC;wBACA;qBACD;YACH;YACA,eAAe,QAAQ,QAAQ,CAG7B;gBACA,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAK,CAAC;wBAC/B,KAAK,CAAC,CAAC,EAAE,WAAW;wBACpB,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,SAAS,EAAE,GAAK;wBACjD;4BAAE,MAAM;4BAAW,IAAI;wBAAU;qBAClC;YACH;YACA,eAAe,QAAQ,QAAQ,CAAe;gBAC5C,OAAO,CAAC,YAAc,CAAC;wBACrB,KAAK,CAAC,CAAC,EAAE,WAAW;wBACpB,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;iBAAU;YAC9B;YACA,iBAAiB,QAAQ,QAAQ,CAG/B;gBACA,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAK,CAAC;wBAC/B,KAAK,CAAC,CAAC,EAAE,UAAU,UAAU,CAAC;wBAC9B,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;oBAAY;iBAAU;YAC1C;YACA,aAAa,QAAQ,KAAK,CAAyC;gBACjE,OAAO,CAAC,EAAE,SAAS,EAAE,GAAG,QAAQ,GAAK,CAAC;wBACpC,KAAK,CAAC,CAAC,EAAE,UAAU,SAAS,CAAC;wBAC7B;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAU;YAC3B;YACA,kBAAkB,QAAQ,QAAQ,CAGhC;gBACA,OAAO,CAAC,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,GAAK,CAAC;wBACnD,KAAK,CAAC,CAAC,EAAE,UAAU,kBAAkB,CAAC;wBACtC,QAAQ;wBACR,MAAM;4BAAE;4BAAY;wBAAa;oBACnC,CAAC;gBACD,iBAAiB;oBAAC;iBAAU;YAC9B;YACA,gBAAgB,QAAQ,QAAQ,CAG9B;gBACA,OAAO,CAAC,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,GAAK,CAAC;wBACjD,KAAK,CAAC,CAAC,EAAE,UAAU,gBAAgB,CAAC;wBACpC,QAAQ;wBACR,MAAM;4BAAE;4BAAY;wBAAW;oBACjC,CAAC;gBACD,iBAAiB;oBAAC;iBAAU;YAC9B;YACA,iBAAiB,QAAQ,KAAK,CAS5B;gBACA,OAAO,CAAC,YAAc,CAAC,CAAC,EAAE,UAAU,MAAM,CAAC;gBAC3C,cAAc,CAAC,QAAQ,OAAO,YAAc;wBAC1C;4BAAE,MAAM;4BAAW,IAAI,GAAG,UAAU,MAAM,CAAC;wBAAC;qBAC7C;YACH;QACF,CAAC;AACH;AAEO,MAAM,EACX,mBAAmB,EACnB,wBAAwB,EACxB,kBAAkB,EAClB,wBAAwB,EACxB,wBAAwB,EACxB,0BAA0B,EAC1B,mBAAmB,EACnB,2BAA2B,EAC3B,yBAAyB,EACzB,uBAAuB,EACxB,GAAG", "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/store/api/translationApi.ts"], "sourcesContent": ["import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';\nimport {\n  TranslationSegment,\n  UpdateSegmentRequest,\n  GetSegmentResponse,\n  Comment,\n  Review,\n  CreateCommentRequest,\n  CreateReviewRequest,\n} from '@/types';\n\nexport const translationApi = createApi({\n  reducerPath: 'translationApi',\n  baseQuery: fetchBaseQuery({\n    baseUrl: '/api/segments',\n    prepareHeaders: (headers) => {\n      headers.set('Content-Type', 'application/json');\n      return headers;\n    },\n  }),\n  tagTypes: ['Segment', 'Comment', 'Review'],\n  endpoints: (builder) => ({\n    getSegment: builder.query<GetSegmentResponse, string>({\n      query: (segmentId) => `/${segmentId}`,\n      providesTags: (result, error, segmentId) => [\n        { type: 'Segment', id: segmentId },\n        'Comment',\n        'Review',\n      ],\n    }),\n    updateSegment: builder.mutation<\n      TranslationSegment,\n      { segmentId: string; data: UpdateSegmentRequest }\n    >({\n      query: ({ segmentId, data }) => ({\n        url: `/${segmentId}`,\n        method: 'PUT',\n        body: data,\n      }),\n      invalidatesTags: (result, error, { segmentId }) => [\n        { type: 'Segment', id: segmentId },\n      ],\n      // Optimistic update for better UX\n      async onQueryStarted({ segmentId, data }, { dispatch, queryFulfilled }) {\n        const patchResult = dispatch(\n          translationApi.util.updateQueryData('getSegment', segmentId, (draft) => {\n            Object.assign(draft.segment, data);\n          })\n        );\n        try {\n          await queryFulfilled;\n        } catch {\n          patchResult.undo();\n        }\n      },\n    }),\n    createComment: builder.mutation<\n      Comment,\n      { segmentId: string; data: CreateCommentRequest }\n    >({\n      query: ({ segmentId, data }) => ({\n        url: `/${segmentId}/comments`,\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: ['Comment'],\n    }),\n    updateComment: builder.mutation<\n      Comment,\n      { commentId: string; data: { content: string; resolved?: boolean } }\n    >({\n      query: ({ commentId, data }) => ({\n        url: `/comments/${commentId}`,\n        method: 'PUT',\n        body: data,\n      }),\n      invalidatesTags: ['Comment'],\n    }),\n    deleteComment: builder.mutation<void, string>({\n      query: (commentId) => ({\n        url: `/comments/${commentId}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: ['Comment'],\n    }),\n    createReview: builder.mutation<\n      Review,\n      { segmentId: string; data: CreateReviewRequest }\n    >({\n      query: ({ segmentId, data }) => ({\n        url: `/${segmentId}/reviews`,\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: ['Review'],\n    }),\n    getSegmentHistory: builder.query<\n      Array<{\n        id: string;\n        segmentId: string;\n        previousText: string;\n        newText: string;\n        changedBy: string;\n        changeType: string;\n        timestamp: string;\n      }>,\n      string\n    >({\n      query: (segmentId) => `/${segmentId}/history`,\n    }),\n    bulkUpdateSegments: builder.mutation<\n      void,\n      { segmentIds: string[]; data: UpdateSegmentRequest }\n    >({\n      query: ({ segmentIds, data }) => ({\n        url: '/bulk-update',\n        method: 'PUT',\n        body: { segmentIds, data },\n      }),\n      invalidatesTags: ['Segment'],\n    }),\n    getTranslationMemoryMatches: builder.query<\n      Array<{\n        sourceText: string;\n        targetText: string;\n        similarity: number;\n        origin: string;\n        lastUsed: string;\n      }>,\n      { projectId: string; sourceText: string; targetLanguage: string }\n    >({\n      query: ({ projectId, sourceText, targetLanguage }) => ({\n        url: '/translation-memory',\n        params: { projectId, sourceText, targetLanguage },\n      }),\n    }),\n  }),\n});\n\nexport const {\n  useGetSegmentQuery,\n  useUpdateSegmentMutation,\n  useCreateCommentMutation,\n  useUpdateCommentMutation,\n  useDeleteCommentMutation,\n  useCreateReviewMutation,\n  useGetSegmentHistoryQuery,\n  useBulkUpdateSegmentsMutation,\n  useGetTranslationMemoryMatchesQuery,\n} = translationApi;\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AAAA;;AAWO,MAAM,iBAAiB,CAAA,GAAA,kNAAA,CAAA,YAAS,AAAD,EAAE;IACtC,aAAa;IACb,WAAW,CAAA,GAAA,gLAAA,CAAA,iBAAc,AAAD,EAAE;QACxB,SAAS;QACT,gBAAgB,CAAC;YACf,QAAQ,GAAG,CAAC,gBAAgB;YAC5B,OAAO;QACT;IACF;IACA,UAAU;QAAC;QAAW;QAAW;KAAS;IAC1C,WAAW,CAAC,UAAY,CAAC;YACvB,YAAY,QAAQ,KAAK,CAA6B;gBACpD,OAAO,CAAC,YAAc,CAAC,CAAC,EAAE,WAAW;gBACrC,cAAc,CAAC,QAAQ,OAAO,YAAc;wBAC1C;4BAAE,MAAM;4BAAW,IAAI;wBAAU;wBACjC;wBACA;qBACD;YACH;YACA,eAAe,QAAQ,QAAQ,CAG7B;gBACA,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAK,CAAC;wBAC/B,KAAK,CAAC,CAAC,EAAE,WAAW;wBACpB,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,SAAS,EAAE,GAAK;wBACjD;4BAAE,MAAM;4BAAW,IAAI;wBAAU;qBAClC;gBACD,kCAAkC;gBAClC,MAAM,gBAAe,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,cAAc,EAAE;oBACpE,MAAM,cAAc,SAClB,eAAe,IAAI,CAAC,eAAe,CAAC,cAAc,WAAW,CAAC;wBAC5D,OAAO,MAAM,CAAC,MAAM,OAAO,EAAE;oBAC/B;oBAEF,IAAI;wBACF,MAAM;oBACR,EAAE,OAAM;wBACN,YAAY,IAAI;oBAClB;gBACF;YACF;YACA,eAAe,QAAQ,QAAQ,CAG7B;gBACA,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAK,CAAC;wBAC/B,KAAK,CAAC,CAAC,EAAE,UAAU,SAAS,CAAC;wBAC7B,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAU;YAC9B;YACA,eAAe,QAAQ,QAAQ,CAG7B;gBACA,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAK,CAAC;wBAC/B,KAAK,CAAC,UAAU,EAAE,WAAW;wBAC7B,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAU;YAC9B;YACA,eAAe,QAAQ,QAAQ,CAAe;gBAC5C,OAAO,CAAC,YAAc,CAAC;wBACrB,KAAK,CAAC,UAAU,EAAE,WAAW;wBAC7B,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;iBAAU;YAC9B;YACA,cAAc,QAAQ,QAAQ,CAG5B;gBACA,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAK,CAAC;wBAC/B,KAAK,CAAC,CAAC,EAAE,UAAU,QAAQ,CAAC;wBAC5B,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAS;YAC7B;YACA,mBAAmB,QAAQ,KAAK,CAW9B;gBACA,OAAO,CAAC,YAAc,CAAC,CAAC,EAAE,UAAU,QAAQ,CAAC;YAC/C;YACA,oBAAoB,QAAQ,QAAQ,CAGlC;gBACA,OAAO,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,GAAK,CAAC;wBAChC,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAY;wBAAK;oBAC3B,CAAC;gBACD,iBAAiB;oBAAC;iBAAU;YAC9B;YACA,6BAA6B,QAAQ,KAAK,CASxC;gBACA,OAAO,CAAC,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,GAAK,CAAC;wBACrD,KAAK;wBACL,QAAQ;4BAAE;4BAAW;4BAAY;wBAAe;oBAClD,CAAC;YACH;QACF,CAAC;AACH;AAEO,MAAM,EACX,kBAAkB,EAClB,wBAAwB,EACxB,wBAAwB,EACxB,wBAAwB,EACxB,wBAAwB,EACxB,uBAAuB,EACvB,yBAAyB,EACzB,6BAA6B,EAC7B,mCAAmC,EACpC,GAAG", "debugId": null}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/store/api/terminologyApi.ts"], "sourcesContent": ["import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';\nimport {\n  TerminologyEntry,\n  CreateTerminologyRequest,\n  UpdateTerminologyRequest,\n  GetTerminologyResponse,\n} from '@/types';\n\ninterface GetTerminologyParams {\n  projectId: string;\n  search?: string;\n  category?: string;\n  language?: string;\n  status?: string;\n  page?: number;\n  limit?: number;\n}\n\ninterface ImportTerminologyRequest {\n  file: File;\n  format: 'csv' | 'tmx' | 'excel';\n  mapping: {\n    sourceTerm: string;\n    targetTerm: string;\n    targetLanguage: string;\n    category?: string;\n    context?: string;\n    usageNotes?: string;\n  };\n  overwriteExisting: boolean;\n}\n\ninterface ImportTerminologyResponse {\n  imported: number;\n  errors: Array<{\n    row: number;\n    error: string;\n  }>;\n  duplicates: Array<{\n    sourceTerm: string;\n    targetTerm: string;\n  }>;\n}\n\nexport const terminologyApi = createApi({\n  reducerPath: 'terminologyApi',\n  baseQuery: fetchBaseQuery({\n    baseUrl: '/api/terminology',\n    prepareHeaders: (headers) => {\n      headers.set('Content-Type', 'application/json');\n      return headers;\n    },\n  }),\n  tagTypes: ['Terminology', 'TerminologyStats'],\n  endpoints: (builder) => ({\n    getTerminology: builder.query<GetTerminologyResponse, GetTerminologyParams>({\n      query: ({ projectId, ...params }) => ({\n        url: `/projects/${projectId}`,\n        params,\n      }),\n      providesTags: ['Terminology'],\n    }),\n    createTerminologyEntry: builder.mutation<\n      TerminologyEntry,\n      { projectId: string; data: CreateTerminologyRequest }\n    >({\n      query: ({ projectId, data }) => ({\n        url: `/projects/${projectId}`,\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: ['Terminology', 'TerminologyStats'],\n    }),\n    updateTerminologyEntry: builder.mutation<\n      TerminologyEntry,\n      { entryId: string; data: UpdateTerminologyRequest }\n    >({\n      query: ({ entryId, data }) => ({\n        url: `/${entryId}`,\n        method: 'PUT',\n        body: data,\n      }),\n      invalidatesTags: ['Terminology'],\n    }),\n    deleteTerminologyEntry: builder.mutation<void, string>({\n      query: (entryId) => ({\n        url: `/${entryId}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: ['Terminology', 'TerminologyStats'],\n    }),\n    approveTerminologyEntry: builder.mutation<\n      TerminologyEntry,\n      { entryId: string; approved: boolean }\n    >({\n      query: ({ entryId, approved }) => ({\n        url: `/${entryId}/approve`,\n        method: 'PUT',\n        body: { approved },\n      }),\n      invalidatesTags: ['Terminology'],\n    }),\n    importTerminology: builder.mutation<\n      ImportTerminologyResponse,\n      { projectId: string; data: FormData }\n    >({\n      query: ({ projectId, data }) => ({\n        url: `/projects/${projectId}/import`,\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: ['Terminology', 'TerminologyStats'],\n    }),\n    exportTerminology: builder.mutation<\n      { downloadUrl: string; expiresAt: string },\n      { projectId: string; format: string; language?: string }\n    >({\n      query: ({ projectId, format, language }) => ({\n        url: `/projects/${projectId}/export`,\n        method: 'POST',\n        body: { format, language },\n      }),\n    }),\n    searchTerminology: builder.query<\n      Array<{\n        entry: TerminologyEntry;\n        confidence: number;\n        context: string;\n      }>,\n      { projectId: string; text: string; targetLanguage: string }\n    >({\n      query: ({ projectId, text, targetLanguage }) => ({\n        url: `/projects/${projectId}/search`,\n        params: { text, targetLanguage },\n      }),\n    }),\n    getTerminologyStats: builder.query<\n      {\n        totalEntries: number;\n        approvedEntries: number;\n        pendingEntries: number;\n        rejectedEntries: number;\n        categoriesCount: Record<string, number>;\n        languagesCount: Record<string, number>;\n      },\n      string\n    >({\n      query: (projectId) => `/projects/${projectId}/stats`,\n      providesTags: ['TerminologyStats'],\n    }),\n    bulkApproveTerminology: builder.mutation<\n      void,\n      { entryIds: string[]; approved: boolean }\n    >({\n      query: ({ entryIds, approved }) => ({\n        url: '/bulk-approve',\n        method: 'PUT',\n        body: { entryIds, approved },\n      }),\n      invalidatesTags: ['Terminology'],\n    }),\n    bulkDeleteTerminology: builder.mutation<void, string[]>({\n      query: (entryIds) => ({\n        url: '/bulk-delete',\n        method: 'DELETE',\n        body: { entryIds },\n      }),\n      invalidatesTags: ['Terminology', 'TerminologyStats'],\n    }),\n  }),\n});\n\nexport const {\n  useGetTerminologyQuery,\n  useCreateTerminologyEntryMutation,\n  useUpdateTerminologyEntryMutation,\n  useDeleteTerminologyEntryMutation,\n  useApproveTerminologyEntryMutation,\n  useImportTerminologyMutation,\n  useExportTerminologyMutation,\n  useSearchTerminologyQuery,\n  useGetTerminologyStatsQuery,\n  useBulkApproveTerminologyMutation,\n  useBulkDeleteTerminologyMutation,\n} = terminologyApi;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;;AA4CO,MAAM,iBAAiB,CAAA,GAAA,kNAAA,CAAA,YAAS,AAAD,EAAE;IACtC,aAAa;IACb,WAAW,CAAA,GAAA,gLAAA,CAAA,iBAAc,AAAD,EAAE;QACxB,SAAS;QACT,gBAAgB,CAAC;YACf,QAAQ,GAAG,CAAC,gBAAgB;YAC5B,OAAO;QACT;IACF;IACA,UAAU;QAAC;QAAe;KAAmB;IAC7C,WAAW,CAAC,UAAY,CAAC;YACvB,gBAAgB,QAAQ,KAAK,CAA+C;gBAC1E,OAAO,CAAC,EAAE,SAAS,EAAE,GAAG,QAAQ,GAAK,CAAC;wBACpC,KAAK,CAAC,UAAU,EAAE,WAAW;wBAC7B;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAc;YAC/B;YACA,wBAAwB,QAAQ,QAAQ,CAGtC;gBACA,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAK,CAAC;wBAC/B,KAAK,CAAC,UAAU,EAAE,WAAW;wBAC7B,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;oBAAe;iBAAmB;YACtD;YACA,wBAAwB,QAAQ,QAAQ,CAGtC;gBACA,OAAO,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,GAAK,CAAC;wBAC7B,KAAK,CAAC,CAAC,EAAE,SAAS;wBAClB,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAc;YAClC;YACA,wBAAwB,QAAQ,QAAQ,CAAe;gBACrD,OAAO,CAAC,UAAY,CAAC;wBACnB,KAAK,CAAC,CAAC,EAAE,SAAS;wBAClB,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;oBAAe;iBAAmB;YACtD;YACA,yBAAyB,QAAQ,QAAQ,CAGvC;gBACA,OAAO,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAK,CAAC;wBACjC,KAAK,CAAC,CAAC,EAAE,QAAQ,QAAQ,CAAC;wBAC1B,QAAQ;wBACR,MAAM;4BAAE;wBAAS;oBACnB,CAAC;gBACD,iBAAiB;oBAAC;iBAAc;YAClC;YACA,mBAAmB,QAAQ,QAAQ,CAGjC;gBACA,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAK,CAAC;wBAC/B,KAAK,CAAC,UAAU,EAAE,UAAU,OAAO,CAAC;wBACpC,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;oBAAe;iBAAmB;YACtD;YACA,mBAAmB,QAAQ,QAAQ,CAGjC;gBACA,OAAO,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAK,CAAC;wBAC3C,KAAK,CAAC,UAAU,EAAE,UAAU,OAAO,CAAC;wBACpC,QAAQ;wBACR,MAAM;4BAAE;4BAAQ;wBAAS;oBAC3B,CAAC;YACH;YACA,mBAAmB,QAAQ,KAAK,CAO9B;gBACA,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,cAAc,EAAE,GAAK,CAAC;wBAC/C,KAAK,CAAC,UAAU,EAAE,UAAU,OAAO,CAAC;wBACpC,QAAQ;4BAAE;4BAAM;wBAAe;oBACjC,CAAC;YACH;YACA,qBAAqB,QAAQ,KAAK,CAUhC;gBACA,OAAO,CAAC,YAAc,CAAC,UAAU,EAAE,UAAU,MAAM,CAAC;gBACpD,cAAc;oBAAC;iBAAmB;YACpC;YACA,wBAAwB,QAAQ,QAAQ,CAGtC;gBACA,OAAO,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAK,CAAC;wBAClC,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAU;wBAAS;oBAC7B,CAAC;gBACD,iBAAiB;oBAAC;iBAAc;YAClC;YACA,uBAAuB,QAAQ,QAAQ,CAAiB;gBACtD,OAAO,CAAC,WAAa,CAAC;wBACpB,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;wBAAS;oBACnB,CAAC;gBACD,iBAAiB;oBAAC;oBAAe;iBAAmB;YACtD;QACF,CAAC;AACH;AAEO,MAAM,EACX,sBAAsB,EACtB,iCAAiC,EACjC,iCAAiC,EACjC,iCAAiC,EACjC,kCAAkC,EAClC,4BAA4B,EAC5B,4BAA4B,EAC5B,yBAAyB,EACzB,2BAA2B,EAC3B,iCAAiC,EACjC,gCAAgC,EACjC,GAAG", "debugId": null}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/store/slices/authSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\nimport { User } from '@/types';\n\ninterface AuthState {\n  user: User | null;\n  isLoading: boolean;\n  isAuthenticated: boolean;\n  currentOrganizationId: string | null;\n}\n\nconst initialState: AuthState = {\n  user: null,\n  isLoading: true,\n  isAuthenticated: false,\n  currentOrganizationId: null,\n};\n\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    setUser: (state, action: PayloadAction<User | null>) => {\n      state.user = action.payload;\n      state.isAuthenticated = !!action.payload;\n      state.isLoading = false;\n    },\n    setLoading: (state, action: PayloadAction<boolean>) => {\n      state.isLoading = action.payload;\n    },\n    setCurrentOrganization: (state, action: PayloadAction<string | null>) => {\n      state.currentOrganizationId = action.payload;\n    },\n    logout: (state) => {\n      state.user = null;\n      state.isAuthenticated = false;\n      state.currentOrganizationId = null;\n      state.isLoading = false;\n    },\n  },\n});\n\nexport const { setUser, setLoading, setCurrentOrganization, logout } = authSlice.actions;\nexport default authSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAUA,MAAM,eAA0B;IAC9B,MAAM;IACN,WAAW;IACX,iBAAiB;IACjB,uBAAuB;AACzB;AAEA,MAAM,YAAY,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC5B,MAAM;IACN;IACA,UAAU;QACR,SAAS,CAAC,OAAO;YACf,MAAM,IAAI,GAAG,OAAO,OAAO;YAC3B,MAAM,eAAe,GAAG,CAAC,CAAC,OAAO,OAAO;YACxC,MAAM,SAAS,GAAG;QACpB;QACA,YAAY,CAAC,OAAO;YAClB,MAAM,SAAS,GAAG,OAAO,OAAO;QAClC;QACA,wBAAwB,CAAC,OAAO;YAC9B,MAAM,qBAAqB,GAAG,OAAO,OAAO;QAC9C;QACA,QAAQ,CAAC;YACP,MAAM,IAAI,GAAG;YACb,MAAM,eAAe,GAAG;YACxB,MAAM,qBAAqB,GAAG;YAC9B,MAAM,SAAS,GAAG;QACpB;IACF;AACF;AAEO,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,sBAAsB,EAAE,MAAM,EAAE,GAAG,UAAU,OAAO;uCACzE,UAAU,OAAO", "debugId": null}}, {"offset": {"line": 611, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/store/slices/uiSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\n\ninterface UIState {\n  sidebarOpen: boolean;\n  theme: 'light' | 'dark' | 'system';\n  notifications: Notification[];\n  activeModal: string | null;\n  loading: {\n    [key: string]: boolean;\n  };\n}\n\ninterface Notification {\n  id: string;\n  type: 'success' | 'error' | 'warning' | 'info';\n  title: string;\n  message: string;\n  timestamp: string;\n  read: boolean;\n}\n\nconst initialState: UIState = {\n  sidebarOpen: true,\n  theme: 'system',\n  notifications: [],\n  activeModal: null,\n  loading: {},\n};\n\nconst uiSlice = createSlice({\n  name: 'ui',\n  initialState,\n  reducers: {\n    toggleSidebar: (state) => {\n      state.sidebarOpen = !state.sidebarOpen;\n    },\n    setSidebarOpen: (state, action: PayloadAction<boolean>) => {\n      state.sidebarOpen = action.payload;\n    },\n    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'system'>) => {\n      state.theme = action.payload;\n    },\n    addNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'timestamp' | 'read'>>) => {\n      const notification: Notification = {\n        ...action.payload,\n        id: Date.now().toString(),\n        timestamp: new Date().toISOString(),\n        read: false,\n      };\n      state.notifications.unshift(notification);\n    },\n    markNotificationRead: (state, action: PayloadAction<string>) => {\n      const notification = state.notifications.find(n => n.id === action.payload);\n      if (notification) {\n        notification.read = true;\n      }\n    },\n    removeNotification: (state, action: PayloadAction<string>) => {\n      state.notifications = state.notifications.filter(n => n.id !== action.payload);\n    },\n    clearNotifications: (state) => {\n      state.notifications = [];\n    },\n    setActiveModal: (state, action: PayloadAction<string | null>) => {\n      state.activeModal = action.payload;\n    },\n    setLoading: (state, action: PayloadAction<{ key: string; loading: boolean }>) => {\n      state.loading[action.payload.key] = action.payload.loading;\n    },\n  },\n});\n\nexport const {\n  toggleSidebar,\n  setSidebarOpen,\n  setTheme,\n  addNotification,\n  markNotificationRead,\n  removeNotification,\n  clearNotifications,\n  setActiveModal,\n  setLoading,\n} = uiSlice.actions;\n\nexport default uiSlice.reducer;\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAqBA,MAAM,eAAwB;IAC5B,aAAa;IACb,OAAO;IACP,eAAe,EAAE;IACjB,aAAa;IACb,SAAS,CAAC;AACZ;AAEA,MAAM,UAAU,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC1B,MAAM;IACN;IACA,UAAU;QACR,eAAe,CAAC;YACd,MAAM,WAAW,GAAG,CAAC,MAAM,WAAW;QACxC;QACA,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;QACA,UAAU,CAAC,OAAO;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QACA,iBAAiB,CAAC,OAAO;YACvB,MAAM,eAA6B;gBACjC,GAAG,OAAO,OAAO;gBACjB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,WAAW,IAAI,OAAO,WAAW;gBACjC,MAAM;YACR;YACA,MAAM,aAAa,CAAC,OAAO,CAAC;QAC9B;QACA,sBAAsB,CAAC,OAAO;YAC5B,MAAM,eAAe,MAAM,aAAa,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,OAAO;YAC1E,IAAI,cAAc;gBAChB,aAAa,IAAI,GAAG;YACtB;QACF;QACA,oBAAoB,CAAC,OAAO;YAC1B,MAAM,aAAa,GAAG,MAAM,aAAa,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,OAAO;QAC/E;QACA,oBAAoB,CAAC;YACnB,MAAM,aAAa,GAAG,EAAE;QAC1B;QACA,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;QACA,YAAY,CAAC,OAAO;YAClB,MAAM,OAAO,CAAC,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,OAAO,OAAO,CAAC,OAAO;QAC5D;IACF;AACF;AAEO,MAAM,EACX,aAAa,EACb,cAAc,EACd,QAAQ,EACR,eAAe,EACf,oBAAoB,EACpB,kBAAkB,EAClB,kBAAkB,EAClB,cAAc,EACd,UAAU,EACX,GAAG,QAAQ,OAAO;uCAEJ,QAAQ,OAAO", "debugId": null}}, {"offset": {"line": 682, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/store/index.ts"], "sourcesContent": ["import { configureStore } from '@reduxjs/toolkit';\nimport { setupListeners } from '@reduxjs/toolkit/query';\nimport { organizationsApi } from './api/organizationsApi';\nimport { projectsApi } from './api/projectsApi';\nimport { translationApi } from './api/translationApi';\nimport { terminologyApi } from './api/terminologyApi';\nimport authSlice from './slices/authSlice';\nimport uiSlice from './slices/uiSlice';\n\nexport const store = configureStore({\n  reducer: {\n    auth: authSlice,\n    ui: uiSlice,\n    [organizationsApi.reducerPath]: organizationsApi.reducer,\n    [projectsApi.reducerPath]: projectsApi.reducer,\n    [translationApi.reducerPath]: translationApi.reducer,\n    [terminologyApi.reducerPath]: terminologyApi.reducer,\n  },\n  middleware: (getDefaultMiddleware) =>\n    getDefaultMiddleware({\n      serializableCheck: {\n        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],\n      },\n    })\n      .concat(organizationsApi.middleware)\n      .concat(projectsApi.middleware)\n      .concat(translationApi.middleware)\n      .concat(terminologyApi.middleware),\n});\n\n// Setup listeners for automatic refetching\nsetupListeners(store.dispatch);\n\nexport type RootState = ReturnType<typeof store.getState>;\nexport type AppDispatch = typeof store.dispatch;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEO,MAAM,QAAQ,CAAA,GAAA,2LAAA,CAAA,iBAAc,AAAD,EAAE;IAClC,SAAS;QACP,MAAM,mIAAA,CAAA,UAAS;QACf,IAAI,iIAAA,CAAA,UAAO;QACX,CAAC,uIAAA,CAAA,mBAAgB,CAAC,WAAW,CAAC,EAAE,uIAAA,CAAA,mBAAgB,CAAC,OAAO;QACxD,CAAC,kIAAA,CAAA,cAAW,CAAC,WAAW,CAAC,EAAE,kIAAA,CAAA,cAAW,CAAC,OAAO;QAC9C,CAAC,qIAAA,CAAA,iBAAc,CAAC,WAAW,CAAC,EAAE,qIAAA,CAAA,iBAAc,CAAC,OAAO;QACpD,CAAC,qIAAA,CAAA,iBAAc,CAAC,WAAW,CAAC,EAAE,qIAAA,CAAA,iBAAc,CAAC,OAAO;IACtD;IACA,YAAY,CAAC,uBACX,qBAAqB;YACnB,mBAAmB;gBACjB,gBAAgB;oBAAC;oBAAmB;iBAAoB;YAC1D;QACF,GACG,MAAM,CAAC,uIAAA,CAAA,mBAAgB,CAAC,UAAU,EAClC,MAAM,CAAC,kIAAA,CAAA,cAAW,CAAC,UAAU,EAC7B,MAAM,CAAC,qIAAA,CAAA,iBAAc,CAAC,UAAU,EAChC,MAAM,CAAC,qIAAA,CAAA,iBAAc,CAAC,UAAU;AACvC;AAEA,2CAA2C;AAC3C,CAAA,GAAA,gLAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,QAAQ", "debugId": null}}, {"offset": {"line": 727, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/adc-platform/services/content/translate-termology/glossaflow/src/components/providers/ReduxProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { Provider } from 'react-redux';\nimport { store } from '@/store';\n\ninterface ReduxProviderProps {\n  children: React.ReactNode;\n}\n\nexport function ReduxProvider({ children }: ReduxProviderProps) {\n  return <Provider store={store}>{children}</Provider>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASO,SAAS,cAAc,EAAE,QAAQ,EAAsB;IAC5D,qBAAO,8OAAC,yJAAA,CAAA,WAAQ;QAAC,OAAO,qHAAA,CAAA,QAAK;kBAAG;;;;;;AAClC", "debugId": null}}]}