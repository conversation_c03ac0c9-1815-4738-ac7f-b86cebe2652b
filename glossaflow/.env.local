# NextAuth Configuration
NEXTAUTH_URL=http://localhost:4000
NEXTAUTH_SECRET=s4fMO/yeBhCjp2PY6znW+m1FUzAMdGAElfgCHZXG6bI=

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://awhtjodmtstnsobmagfc.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.GMfJ55OG6q_OpzDWu0IfKBeQA_4663siNWbkFtvcG84
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.MwbzELJQjJvCbUczrJNEOhVMKufHhRvc0GiQwGJvgko

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# OAuth Providers
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Redis Configuration (Optional - for caching)
UPSTASH_REDIS_REST_URL=your-redis-url
UPSTASH_REDIS_REST_TOKEN=your-redis-token

# Google Cloud Storage Configuration
GCS_BUCKET_NAME=glossaflow-storage-**********
GCS_PROJECT_ID=gen-lang-client-**********
GCS_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GCS_SERVICE_ACCOUNT_KEY_PATH=./storage-service-account.json

# Email Configuration (Optional)
EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>
